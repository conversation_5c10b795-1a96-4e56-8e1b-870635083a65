### TEMPLATES #################################################
include:
  - project: 'dlice/tools/gitlab-*********************/cloud-run-v2'
    ref: '3.0.0'
    file:
      - '/template.gitlab-ci.yml'
  - project: 'to-be-continuous/docker'
    ref: '6.1.7'
    file:
      - '/templates/gitlab-ci-docker.yml'
      - '/templates/********************.yml'
  - project: 'knowledge-bot/ci_utils'
    ref: '0.0.0'
    file:
      - '/bump.gitlab-ci.yml'
      - '/gcp_login.gitlab-ci.yml'
  - project: 'dlice/tools/gitlab-*********************/cloud-scheduler'
    ref: '1.0'
    file:
    - 'template.gitlab-ci.yml'
  - project: 'knowledge-bot/ci_utils'
    ref: main
    file: gitlab-ci-itop.yml
  - project: 'to-be-continuous/sonar'
    ref: '4.3.1'
    file: '/templates/gitlab-ci-sonar.yml'


stages:
  - check
  - test
  - sonar
  - build # TBC Docker
  - package-build # TBC Docker
  - package-test # TBC Docker
  - publish # TBC Docker
  - Start Change
  - cloud-run-deploy
  - Close Change
  - reporting
  - optional-undeploy
  - bump


### CACHE ######################################################
.python_pull_cache: &python_pull_cache
  key: pipenv
  paths:
    - ${PIPENV_CACHE_DIR}
    - ${PIPENV_PATH}
    - ${PIP_CACHE_DIR}
    - ${PRE_COMMIT_HOME}
    - ${PYTHON_VENV_DIR}
  policy: pull


### WORKFLOW

.rule_main: &rule_main
  if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_COMMIT_TITLE !~ /^Release to version/ && $CI_COMMIT_TITLE !~ /^Init to version/

.rule_mr: &rule_mr
  if: $CI_MERGE_REQUEST_ID

.rule_tag: &rule_tag
  if: '$CI_COMMIT_TAG =~ /^\d+\.\d+\.\d+$/'

.vars_env_dev:
  variables:
    ENV_GCP: dev
    GCP_PROJECT_NUMBER: '1001348093840'
    APP_VERSION: ${CI_COMMIT_REF_SLUG}_${CI_COMMIT_SHORT_SHA}
    APP_IMAGE: '${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/loadscheduler-${ENV_GCP}/loadscheduler/snapshot:$APP_VERSION'
    SIDECAR_PROXY_IMAGE: '${GCP_REGION}-docker.pkg.${ENV_GCP}/ofr-ekb-knowledgebot-${ENV_GCP}/proxy-sidecar-${ENV_GCP}/sidecar-proxy/snapshot:0.1.0'
    DEBUG_LOG: 'True'
    KBOT_BACK_API_CLOUD_RUN_URL: 'https://kbot-backend-dev-cloud-run-v2-${GCP_PROJECT_NUMBER}.europe-west3.run.app'
    KBOT_BACK_API_URL: 'https://enabler-knowledge-bot-dev.dev.api.hbx.geo.infra.ftgroup'
    OKAPI_URL: 'https://okapi-v2.api.hbx.geo.infra.ftgroup/v2/token'
    KBOT_LOADSCHEDULER_CLIENT_ID: "kbot_loadscheduler_client_id"
    KBOT_LOADSCHEDULER_CLIENT_SECRET: "kbot_loadscheduler_client_secret"
    KBOT_LOADSCHEDULER_CLIENT_SCOPE: 'api-enabler_knowledge_bot_dev-v1-dev:admin_all'
    KBOT_EMBEDDING_API_URL: 'https://kbot-embedding-dev-cloud-run-v2-${GCP_PROJECT_NUMBER}.europe-west3.run.app'
    KBOT_LOAD_SCHEDULER_API_URL: 'https://kbot-loadscheduler-dev-cloud-run-v2-${GCP_PROJECT_NUMBER}.europe-west3.run.app'
    KBOT_WORK_BUCKET_PREFIX: "gs://ofr-ekb-knowledgebot-work-dev-[perimeter_code]-${ENV_GCP}"
    URL_SERVICE_BASIC: "https://newbasicqualif-m2m.int.api.hbx.geo.infra.ftgroup/api"
    URL_SERVICE_SHAREPOINT: "https://orange0.sharepoint.com/"
    OKAPI_URL_BASIC: "https://okapi-v2.api.hbx.geo.infra.ftgroup/v2/token"
    SERVICE_SCOPE_BASIC: "api-newbasicqualif-v1-int:readonly"
    TIMEOUT_BASIC: 120
    PACKAGE_CF_LAUNCH_LOAD_SCHEDULER_ZIP_NAME: "cf_load_schechuler_launch_$APP_VERSION.zip"
    

.vars_env_ppr:
  variables:
    ENV_GCP: ppr
    GCP_PROJECT_NUMBER: '95809453724'
    APP_VERSION: $CI_COMMIT_SHORT_SHA
    APP_IMAGE: '${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/loadscheduler-${ENV_GCP}/loadscheduler/snapshot:$APP_VERSION'
    SIDECAR_PROXY_IMAGE: '${GCP_REGION}-docker.pkg.dev/ofr-ekb-knowledgebot-${ENV_GCP}/proxy-sidecar-${ENV_GCP}/sidecar-proxy:0.1.0'
    DEBUG_LOG: 'False'
    KBOT_BACK_API_CLOUD_RUN_URL: 'https://kbot-backend-ppr-cloud-run-v2-${GCP_PROJECT_NUMBER}.europe-west3.run.app'
    KBOT_BACK_API_URL: 'https://enabler-knowledge-bot-ppr.integ.api.hbx.geo.infra.ftgroup'
    OKAPI_URL: 'https://okapi-v2.api.hbx.geo.infra.ftgroup/v2/token'
    KBOT_LOADSCHEDULER_CLIENT_ID: "kbot_loadscheduler_client_id"
    KBOT_LOADSCHEDULER_CLIENT_SECRET: "kbot_loadscheduler_client_secret"
    KBOT_LOADSCHEDULER_CLIENT_SCOPE: 'api-enabler_knowledge_bot_ppr-v1-ppd:admin_all'
    KBOT_EMBEDDING_API_URL: 'https://kbot-embedding-ppr-cloud-run-v2-${GCP_PROJECT_NUMBER}.europe-west3.run.app'
    KBOT_LOAD_SCHEDULER_API_URL: 'https://kbot-loadscheduler-ppr-cloud-run-v2-${GCP_PROJECT_NUMBER}.europe-west3.run.app'
    KBOT_WORK_BUCKET_PREFIX: "gs://ofr-ekb-knowledgebot-work-dev-[perimeter_code]-${ENV_GCP}"
    URL_SERVICE_BASIC: "https://newbasicpreprod-m2m.int.api.hbx.geo.infra.ftgroup/api"
    URL_SERVICE_SHAREPOINT: "https://orange0.sharepoint.com/"
    OKAPI_URL_BASIC: "https://okapi-v2.api.hbx.geo.infra.ftgroup/v2/token"
    SERVICE_SCOPE_BASIC: "api-newbasicpreprod-v1-ppd:readonly"
    TIMEOUT_BASIC: 120
    PACKAGE_CF_LAUNCH_LOAD_SCHEDULER_ZIP_NAME: "cf_load_schechuler_launch_$APP_VERSION.zip"

.vars_env_prd:
  variables:
    ENV_GCP: prd
    GCP_PROJECT_NUMBER: '94243238635'
    APP_VERSION: $CI_COMMIT_TAG
    APP_IMAGE: '${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/loadscheduler-${ENV_GCP}/loadscheduler:$APP_VERSION'
    SIDECAR_PROXY_IMAGE: '${GCP_REGION}-docker.pkg.dev/ofr-ekb-knowledgebot-${ENV_GCP}/proxy-sidecar-${ENV_GCP}/sidecar-proxy:0.1.0'
    DEBUG_LOG: 'False'
    KBOT_BACK_API_CLOUD_RUN_URL: 'https://kbot-backend-prd-cloud-run-v2-${GCP_PROJECT_NUMBER}.europe-west3.run.app'
    KBOT_BACK_API_URL: 'https://enabler-knowledge-bot-prd.prod.api.hbx.geo.infra.ftgroup'
    OKAPI_URL: 'https://okapi-v2.api.hbx.geo.infra.ftgroup/v2/token'
    KBOT_LOADSCHEDULER_CLIENT_ID: "kbot_loadscheduler_client_id"
    KBOT_LOADSCHEDULER_CLIENT_SECRET: "kbot_loadscheduler_client_secret"
    KBOT_LOADSCHEDULER_CLIENT_SCOPE: 'api-enabler_knowledge_bot_prd-v1-prd:admin_all'
    KBOT_EMBEDDING_API_URL: 'https://kbot-embedding-prd-cloud-run-v2-${GCP_PROJECT_NUMBER}.europe-west3.run.app'
    KBOT_LOAD_SCHEDULER_API_URL: 'https://kbot-loadscheduler-prd-cloud-run-v2-${GCP_PROJECT_NUMBER}.europe-west3.run.app'
    KBOT_WORK_BUCKET_PREFIX: "gs://ofr-ekb-knowledgebot-work-dev-[perimeter_code]-${ENV_GCP}"
    URL_SERVICE_BASIC: "https://newbasicprod-m2m.api.hbx.geo.infra.ftgroup/api"
    URL_SERVICE_SHAREPOINT: "https://orange0.sharepoint.com/"
    OKAPI_URL_BASIC: "https://okapi-v2.api.hbx.geo.infra.ftgroup/v2/token"
    SERVICE_SCOPE_BASIC: "api-newbasicprod-v1-prd:readonly"
    TIMEOUT_BASIC: 120
    PACKAGE_CF_LAUNCH_LOAD_SCHEDULER_ZIP_NAME: "cf_load_schechuler_launch_$APP_VERSION.zip"


.cloud-scheduler-launch-load-scheduler: &cloud-scheduler-launch-load-scheduler
  TERRAFORM_STATE_KEY: 'cs_kbot_launch_load_scheduler_${perimeter}_${ENV_GCP}'
  CLOUD_SCHEDULER_CONFIG: '{
    "name": "cs_kbot_launch_load_scheduler_${perimeter}",
    "project_id": "$GCP_PROJECT_ID",
    "job_name": "cs_kbot_launch_load_scheduler_${perimeter}",
    "description": "cloud scheduler to launch kbot load scheduler pour le perimetre ${perimeter}",
    "job_schedule": "$crontab",
    "time_zone": "Europe/Paris",
    "region": "${GCP_REGION}",
    "attempt_deadline": "600s",
    "job_conf": {
    "http_target": {
    "http_method": "POST",
    "body": "{
    }",
    "headers": {
    "User-Agent": "Google-Cloud-Scheduler",
    "Content-Type": "application/json"
    },
    "uri": "${KBOT_LOAD_SCHEDULER_API_URL}/schedule/treatments/${perimeter}/launch",
    "service_account_email": "$GCP_SA_CR_EX",
    "auth_method": "oidc"
    }
    }
    }'

.cloud-run-loadscheduler:
  extends: .cloud_run_v2
  variables:
    GCP_PROJECT: '${GCP_PROJECT_ID}'
    GCP_OIDC_ACCOUNT: 'sa-for-cicd-${CI_PROJECT_ID}@${GCP_PROJECT_ID}.iam.gserviceaccount.com'
    GCP_OIDC_PROVIDER: 'projects/${GCP_PROJECT_NUMBER}/locations/global/workloadIdentityPools/${GCP_WIF_POOL}/providers/${GCP_WIF_PROVIDER}'
    TERRAFORM_STATE_KEY: "loadscheduler-v2-state-$ENV"
    CLOUD_RUN_V2_CONFIG: '{
      "project_id": "$GCP_PROJECT_ID",
      "name": "kbot-loadscheduler-${ENV_GCP}-cloud-run-v2",
      "location": "$GCP_REGION",
      "ingress": "INGRESS_TRAFFIC_INTERNAL_ONLY",
      "service_account": "${GCP_SA_CR_EX}",
      "timeout": "600s",
      "volumes": [
        {
          "name": "kbot_loadscheduler_client_id",
          "secret": {
            "secret": "kbot_loadscheduler_client_id",
            "items": [{"version": "latest", "path": "secret"}]
          }
        },
        {
          "name": "kbot_loadscheduler_client_secret",
          "secret": {
            "secret": "kbot_loadscheduler_client_secret",
            "items": [{"version": "latest", "path": "secret"}]
          }
        },
        {
          "name": "sharepoint-client-config",
          "secret": {
            "secret": "sharepoint-client-config",
            "items": [{"version": "latest", "path": "secret"}]
          }
        },
        {
          "name": "sharepoint-client-private-key",
          "secret": {
            "secret": "sharepoint-client-private-key",
            "items": [{"version": "latest", "path": "secret"}]
          }
        },
        {
          "name": "basic-client-id",
          "secret": {
            "secret": "basic-client-id",
            "items": [{"version": "latest", "path": "secret"}]
          }
        },
        {
          "name": "basic-client-secret",
          "secret": {
            "secret": "basic-client-secret",
            "items": [{"version": "latest", "path": "secret"}]
          }
        },
        {
          "name": "ca_certificate",
          "secret": {
            "secret": "ca_certificate",
            "items": [{"version": "latest", "path": "secret"}]
          }
        }
      ],
      "containers": [
        {
          "container_image": "$APP_IMAGE",
          "container_name" : "container-1",
          "env" : {
            "ENV": "$ENV_GCP",
            "VERSION": "$APP_VERSION",
            "DEBUG": "${DEBUG_LOG}", 
            "DEPLOY_ENVIRONMENT": "$ENV_GCP", 
            "GCP_PROJECT_ID": "$GCP_PROJECT_ID", 
            "PROJECT_REGION": "$GCP_REGION", 
            "PROJECT_NUMBER": "$GCP_PROJECT_NUMBER",
            "PATH_TO_SECRET_CONFIG": "/etc/secrets",
            "KBOT_BACK_API_CLOUD_RUN_URL": "$KBOT_BACK_API_CLOUD_RUN_URL",
            "KBOT_BACK_API_URL": "$KBOT_BACK_API_URL",
            "OKAPI_URL": "$OKAPI_URL",
            "KBOT_LOADSCHEDULER_CLIENT_ID": "$KBOT_LOADSCHEDULER_CLIENT_ID",
            "KBOT_LOADSCHEDULER_CLIENT_SECRET": "$KBOT_LOADSCHEDULER_CLIENT_SECRET",
            "KBOT_LOADSCHEDULER_CLIENT_SCOPE": "$KBOT_LOADSCHEDULER_CLIENT_SCOPE",
            "KBOT_EMBEDDING_API_URL": "$KBOT_EMBEDDING_API_URL",
            "KBOT_WORK_BUCKET_PREFIX": "$KBOT_WORK_BUCKET_PREFIX",
            "URL_SERVICE_BASIC": "$URL_SERVICE_BASIC",
            "OKAPI_URL_BASIC": "$OKAPI_URL_BASIC",
            "URL_SERVICE_SHAREPOINT": "$URL_SERVICE_SHAREPOINT",
            "SERVICE_SCOPE_BASIC": "$SERVICE_SCOPE_BASIC",
            "TIMEOUT_BASIC": "$TIMEOUT_BASIC",
            "CONFLUENCE_URL": "${CONFLUENCE_URL:-}",
            "DEFAULT_SPACE_KEY": "${DEFAULT_SPACE_KEY:-}",
            "PROXY": "http://localhost:10000",
            "HTTP_PROXY": "http://localhost:10000",
            "HTTPS_PROXY": "http://localhost:10000",
            "no_proxy": "***************,*.europe-west3.run.app,metadata,metadata.google.internal,metadata.google.internal.,.internal,.google.internal,.googleapis.com,.google.com"
          },
          "ports": {
            "name"          : "http1",
            "container_port": 8080
          },
          "resources": {
            "limits": {
              "cpu"   : "1",
              "memory": "2048Mi"
            }
          },
          "volume_mounts": [
            {
              "name": "kbot_loadscheduler_client_id",
              "mount_path": "/etc/secrets/kbot_loadscheduler_client_id"
            },
            {
              "name": "kbot_loadscheduler_client_secret",
              "mount_path": "/etc/secrets/kbot_loadscheduler_client_secret"
            },
            {
              "name": "sharepoint-client-config",
              "mount_path": "/etc/secrets/sharepoint-client-config"
            },
            {
              "name": "sharepoint-client-private-key",
              "mount_path": "/etc/secrets/sharepoint-client-private-key"
            },
            {
              "name": "basic-client-id",
              "mount_path": "/etc/secrets/basic-client-id"
            },
            {
              "name": "basic-client-secret",
              "mount_path": "/etc/secrets/basic-client-secret"
            },
            {
              "name": "ca_certificate",
              "mount_path": "/etc/secrets/ca_certificate"
            }
          ]
        },
        {
          "container_name": "sidecarproxy",
          "container_image" : "$SIDECAR_PROXY_IMAGE",
          "env": {
            "AUDIENCE": "ofr-1h8-fwd-proxy-inet-prd"
          },
          "startup_probe": {
              "initial_delay_seconds" : 10,
              "timeout_seconds" : 3,
              "period_seconds" : 10,
              "failure_threshold" : 10,
              "tcp_socket": {
                "port" : 10000
              }
            }
        }

      ],
      "template_scaling": {
        "min_instance_count": 1,
        "max_instance_count": 1
      },
      "vpc_access": {
        "egress": "ALL_TRAFFIC",
        "network_interfaces" : {
          "network": "ofr-ekb-knowledgebot-vpc-${ENV_GCP}",
          "subnetwork": "ofr-ekb-vpc-cloundrun-directvpc"
        }
      },
      "iam_bindings": [
        {
          "role": "roles/run.invoker",
          "member": "group:<EMAIL>"
        },
        {
          "role": "roles/run.invoker",
          "member": "serviceAccount:sa-load-scheduler-cld-run-ex@${GCP_PROJECT_ID}.iam.gserviceaccount.com"
        }
      ]
    }'


workflow:
  rules:
    - <<: *rule_mr
      variables: !reference [.vars_env_dev, variables]
    - <<: *rule_main
      variables: !reference [.vars_env_ppr, variables]
    - <<: *rule_tag
      variables: !reference [.vars_env_prd, variables]

variables:
  # CI
  SKIP_CI: 'false'

  # Python
  PYTHON_VENV_DIR: .pipenv
  PYTHON_VERSION: '3.12'

  # Cache
  PIP_CACHE_DIR: $CI_PROJECT_DIR/.cache/pip
  PRE_COMMIT_HOME: $CI_PROJECT_DIR/.cache/pre-commit
  PIPENV_CACHE_DIR: .pipenv/pipcache
  PIPENV_PATH: .pipenv
  SKIP_PRE_COMMIT: 'false'
  WORKON_HOME: .pipenv/venvs

  GCP_PROJECT_ID: 'ofr-ekb-knowledgebot-${ENV_GCP}'
  GCP_PROJECT: $GCP_PROJECT_ID

  GCP_SA_CR_EX: 'sa-load-scheduler-cld-run-ex@${GCP_PROJECT_ID}.iam.gserviceaccount.com'

# TBC Terraform
  ENV: $ENV_GCP
  DOCKER_REGISTRY_MIRROR: 'https://dockerproxy.repos.tech.orange'
  GCP_REGION: 'europe-west3'
  GCP_WIF_PROVIDER: 'gitlab-tech-orange'
  GCP_WIF_POOL: 'orange-pool'
  CICD_SA: 'sa-for-cicd-${CI_PROJECT_ID}@${GCP_PROJECT_ID}.iam.gserviceaccount.com'
  GCP_OIDC_PROVIDER: 'projects/${GCP_PROJECT_NUMBER}/locations/global/workloadIdentityPools/${GCP_WIF_POOL}/providers/${GCP_WIF_PROVIDER}'
  GCP_OIDC_ACCOUNT: '${CICD_SA}'

  # TBC Docker
  DOCKER_SNAPSHOT_IMAGE: '${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/loadscheduler-${ENV_GCP}/loadscheduler/snapshot:$APP_VERSION'
  DOCKER_RELEASE_IMAGE: '${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/loadscheduler-${ENV_GCP}/loadscheduler:$APP_VERSION'
  DOCKER_BUILD_CACHE_DISABLED: 'true' # So all docker image layers are rebuilded including apt update/upgrade to fix vulnerabilities in the image

  # TBC Sonar
  # SONAR_HOST_URL defined in CO/CD Variables of the group
  # SONAR_TOKEN defined in CI/CD Variables of the project
  # Others defined in sonar-project.properties

### DEFAULT ###################################################
default:
  image: python:${PYTHON_VERSION}

.activate_pyenv: &activate_pyenv
  - TERM=linux
  - python -V
  - pip -V
  - pip install pipenv --upgrade
  - python -m pip install --upgrade pip
  - pipenv --python ${PYTHON_VERSION}
  - pipenv run pip install -r requirements.txt
  - pipenv run pip install -r tests/test-requirements.txt

### VERSIONING ###########################################################################
bump:
  stage: bump
  extends: .bump
  when: manual

### CHECKING QUALITY AND SECURITY ########################################################

check-dependencies:
  stage: check
  cache:
    <<: *python_pull_cache
    policy: pull-push
  before_script:
    - *activate_pyenv
  script:
    - echo "🔍 Vérification des dépendances Confluence"
    - pipenv run python -c "import aiohttp; print('✅ aiohttp OK')"
    - pipenv run python -c "import beautifulsoup4; print('✅ beautifulsoup4 OK')"
    - pipenv run python -c "import lxml; print('✅ lxml OK')"
    - pipenv run python -c "import pypdf; print('✅ pypdf OK')"
    - pipenv run python -c "import docx; print('✅ python-docx OK')"
    - pipenv run python -c "import openpyxl; print('✅ openpyxl OK')"
    - echo "🔍 Test d'import du module Confluence"
    - pipenv run python -c "from kbotloadscheduler.loader.confluence.confluence_loader import ConfluenceLoader; print('✅ ConfluenceLoader OK')"

check-security:bandit:
  stage: check
  cache:
    <<: *python_pull_cache
    policy: pull-push
  before_script:
    - *activate_pyenv
  script:
    - touch bandit_report.csv
    - pipenv run bandit -c bandit.yaml -f csv -o bandit_report.csv -r src --exit-zero
    - cat bandit_report.csv
  artifacts:
    paths:
      - bandit_report.csv

check-linting:flake8:
  stage: check
  cache:
    <<: *python_pull_cache
    policy: pull-push
  before_script:
    - *activate_pyenv
  script:
    - make flake

### UNITARY TESTS #########################################################################

test-coverage:
  stage: test
  cache:
    <<: *python_pull_cache
    policy: pull-push
  before_script:
    - *activate_pyenv
  script:
    - export ENV="tests"
    - export GCP_PROJECT_ID=""
    - export ROOT_TEST="tests"
    - export PATH_TO_SECRET_CONFIG="conf/etc/secrets/tests"
    - export KBOT_BACK_API_URL="https://kbot-back-api:8080"
    - export KBOT_EMBEDDING_API_URL="https://kbot-embedding-api:8081"
    - export KBOT_WORK_BUCKET_PREFIX="gs://mon_bucket-[perimeter_code]"
    - export URL_SERVICE_SHAREPOINT="https://orange0.sharepoint.com/"
    - export CONFLUENCE_URL="https://test-confluence.example.com"
    - export DEFAULT_SPACE_KEY="TEST"
    - pipenv run python -m pytest --cov-config=.coveragerc --cov-report=html --cov-report term --cov-report=xml --cov=src tests
    - pipenv run coverage report > ${CI_PROJECT_DIR}/report_coverage.txt
  artifacts:
    paths:
      - report_coverage.txt
      - coverage_report/coverage_report.xml

test-confluence-integration:
  stage: test
  cache:
    <<: *python_pull_cache
    policy: pull-push
  before_script:
    - *activate_pyenv
  script:
    - export ENV="tests"
    - export GCP_PROJECT_ID=""
    - export PATH_TO_SECRET_CONFIG="conf/etc/secrets/tests"
    - export CONFLUENCE_URL="https://test-confluence.example.com"
    - export DEFAULT_SPACE_KEY="TEST"
    - echo "🧪 Exécution des tests d'intégration Confluence"
    - pipenv run python -m pytest tests/loader/test_confluence_end_to_end.py -v --disable-warnings
    - pipenv run python -m pytest tests/loader/test_confluence_integration_with_mock_gcs.py -v --disable-warnings
    - pipenv run python -m pytest tests/loader/test_confluence_with_mock_confluence.py -v --disable-warnings
  artifacts:
    when: always
    reports:
      junit: junit-report.xml
    paths:
      - junit-report.xml

### SONAR ##################################################################################

sonar:
  stage: sonar

### REPORTING ##############################################################################

reporting-to-BigQuery:
  stage: reporting
  cache:
    <<: *python_pull_cache
    policy: pull-push
  image: google/cloud-sdk:alpine
  id_tokens:
    GCP_JWT:
      aud: '$CI_SERVER_URL'
  before_script:
    - !reference [.gcp_login, script]
    - gcp_login $GCP_OIDC_PROVIDER $GCP_OIDC_ACCOUNT $GCP_PROJECT_ID $GCP_JWT
  script:
    - bash scripts/bandit_report.sh $GCP_PROJECT_ID kbot_quality loadscheduler
    - bash scripts/coverage_report_for_bq.sh $GCP_PROJECT_ID kbot_quality "$CI_PROJECT_DIR/report_coverage.txt" loadscheduler

### CLOUD-RUN ###############################################################################

cloud-run-deploy:
  extends: .cloud-run-loadscheduler
  stage: cloud-run-deploy
  variables:
    JOB_MODE: 'DEPLOY'
  environment:
    name: "$TERRAFORM_STATE_KEY"
    action: start


### CLOUD SCHEDULER launch-load-scheduler ######################################################################
cloud-sched-launch-load-scheduler-mktsearch:
  stage: cloud-run-deploy
  extends:
    - .cloud_scheduler
  only:
    variables:
      - $ENV_GCP =~ /prd/
  variables:
    perimeter: "mktsearch"
    crontab: "*/10 * * * *"
    <<: *cloud-scheduler-launch-load-scheduler
    JOB_MODE: 'DEPLOY'

cloud-sched-launch-load-scheduler-ebotman:
  stage: cloud-run-deploy
  extends:
    - .cloud_scheduler
  only:
    variables:
      - $ENV_GCP =~ /prd/
      - $ENV_GCP =~ /ppr/
  variables:
    perimeter: "ebotman"
    crontab: "*/10 21-23,0-7 * * *"
    <<: *cloud-scheduler-launch-load-scheduler
    JOB_MODE: 'DEPLOY'

cloud-sched-launch-load-scheduler-sandbox:
  stage: cloud-run-deploy
  extends:
    - .cloud_scheduler
  only:
    variables:
      - $ENV_GCP =~ /ppr/
  variables:
    perimeter: "sandbox"
    crontab: "5-55/10 * * * *"
    <<: *cloud-scheduler-launch-load-scheduler
    JOB_MODE: 'DEPLOY'

### UNDEPLOY RESSOURCEs ###############################################################################

undeploy-cloud-run:
  extends: .cloud-run-loadscheduler
  stage: optional-undeploy
  variables:
    JOB_MODE: "UNDEPLOY"
  environment:
    name: "$TERRAFORM_STATE_KEY"
    action: stop
  when: 'manual'

